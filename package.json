{"name": "betsightly", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:component": "cypress run --component", "test:e2e": "cypress run", "test:a11y": "cypress run --spec \"cypress/e2e/accessibility.cy.ts\"", "test:all": "npm run test && npm run test:component && npm run test:e2e && npm run test:a11y", "cypress:open": "cypress open", "optimize-images": "node scripts/optimize-images.js", "generate-pwa-icons": "node scripts/generate-pwa-icons.js", "optimize": "npm run optimize-images && npm run generate-pwa-icons", "perf": "npm run optimize && npm run build"}, "dependencies": {"@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "decimal.js": "^10.4.3", "file-saver": "^2.0.5", "framer-motion": "^12.11.4", "jspdf": "^2.5.1", "jspdf-autotable": "^3.7.0", "lucide-react": "^0.510.0", "react": "^18.2.0", "react-day-picker": "^9.6.7", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.6", "uuid": "^8.3.2", "web-vitals": "^5.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/preset-env": "^7.22.10", "@babel/preset-react": "^7.22.5", "@babel/preset-typescript": "^7.22.5", "@tailwindcss/postcss": "^4.1.6", "@tailwindcss/vite": "^4.1.6", "@testing-library/cypress": "^10.0.1", "@testing-library/jest-dom": "^6.0.1", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.4", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "axe-core": "^4.7.2", "babel-jest": "^29.6.4", "cssnano": "^7.0.7", "cypress": "^13.0.0", "cypress-axe": "^1.5.0", "cypress-real-events": "^1.10.0", "eslint": "^8.47.0", "eslint-plugin-cypress": "^2.14.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.6.4", "jest-axe": "^8.0.0", "jest-environment-jsdom": "^29.6.4", "postcss": "^8.4.28", "ts-jest": "^29.1.1", "typescript": "^5.0.2", "vite": "^4.4.5"}}