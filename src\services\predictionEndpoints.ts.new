/**
 * Prediction Endpoints Service
 *
 * This file contains functions for fetching prediction data from the unified API endpoint.
 * It provides a single source of truth for all prediction data in the application.
 */

import type { Prediction } from '../types';

/**
 * Fetch all prediction categories from the unified API endpoint
 * @returns A record of prediction categories with their predictions
 */
export async function getAllCategoryPredictions(): Promise<Record<string, Prediction[]>> {
  try {
    console.log('Fetching all prediction categories from unified endpoint');

    // Fetch data from the unified endpoint
    const response = await fetch('http://localhost:8000/api/predictions/categories');
    
    // Log the raw response status
    console.log('API Response Status:', response.status, response.statusText);
    
    // Try to parse the response as JSON
    let data: any = {};
    try {
      const text = await response.text();
      console.log('Raw API response text:', text.substring(0, 500) + (text.length > 500 ? '...' : ''));
      
      // Try to parse as JSON if it looks like JSON
      if (text.trim().startsWith('{') || text.trim().startsWith('[')) {
        data = JSON.parse(text);
        console.log('Parsed API response:', data);
      } else {
        console.warn('API response is not JSON');
      }
    } catch (error) {
      console.error('Error parsing API response:', error);
    }

    // Handle different response formats
    let categoriesData: Record<string, any[]> = {};
    
    if (data && typeof data === 'object') {
      // If data is already in the expected format with categories
      if (data["2_odds"] || data["5_odds"] || data["10_odds"] || data["rollover"]) {
        console.log('Found direct category data in response');
        categoriesData = data;
      } 
      // If data has a categories property
      else if (data.categories && typeof data.categories === 'object') {
        console.log('Found categories property in response');
        categoriesData = data.categories;
      }
      // If data is an array, try to categorize it
      else if (Array.isArray(data)) {
        console.log('Response is an array, categorizing by odds');
        // Categorize predictions by odds
        categoriesData = {
          "2_odds": data.filter((p: any) => p.odds && p.odds <= 2.5),
          "5_odds": data.filter((p: any) => p.odds && p.odds > 2.5 && p.odds <= 5),
          "10_odds": data.filter((p: any) => p.odds && p.odds > 5),
          "rollover": data.filter((p: any) => p.rolloverDay || p.rollover_day)
        };
      }
    }
    
    console.log('Processed categories data:', Object.keys(categoriesData));
    
    // Map the predictions to the frontend format
    const result: Record<string, Prediction[]> = {};
    
    for (const [category, predictions] of Object.entries(categoriesData)) {
      if (Array.isArray(predictions)) {
        console.log(`Processing ${predictions.length} predictions for category ${category}`);
        result[category] = mapPredictionsToFrontendFormat(predictions);
      }
    }
    
    return result;
  } catch (error) {
    console.error("Error fetching all prediction categories:", error);
    throw error;
  }
}

/**
 * Map API predictions to frontend format
 * @param predictions The predictions from the API
 * @returns The predictions in frontend format
 */
function mapPredictionsToFrontendFormat(predictions: any[]): Prediction[] {
  if (!Array.isArray(predictions)) {
    console.warn('Expected predictions to be an array, but got:', typeof predictions);
    return [];
  }
  
  return predictions.map(prediction => {
    // Extract game data
    const game = {
      id: prediction.fixture_id || prediction.game_id || prediction.id || `game-${Math.random().toString(36).substring(2, 9)}`,
      sport: prediction.sport || 'soccer',
      homeTeam: typeof prediction.home_team === 'object' ? prediction.home_team : {
        id: prediction.home_team_id || `team-${Math.random().toString(36).substring(2, 9)}`,
        name: prediction.home_team || 'Home Team',
        logo: prediction.home_team_logo || '/teams/default.png'
      },
      awayTeam: typeof prediction.away_team === 'object' ? prediction.away_team : {
        id: prediction.away_team_id || `team-${Math.random().toString(36).substring(2, 9)}`,
        name: prediction.away_team || 'Away Team',
        logo: prediction.away_team_logo || '/teams/default.png'
      },
      startTime: new Date(prediction.start_time || prediction.date || new Date()),
      league: prediction.league || prediction.competition || 'Unknown League',
      status: prediction.status || 'scheduled'
    };
    
    // Extract prediction data
    const predictionType = prediction.prediction_type || prediction.type || 'Match Result';
    const odds = prediction.odds || 1.5;
    const confidence = prediction.confidence || prediction.confidence_pct || 70;
    const explanation = prediction.explanation || prediction.reason || '';
    
    // Create frontend prediction object
    return {
      id: prediction.id || `prediction-${Math.random().toString(36).substring(2, 9)}`,
      game: game,
      predictionType: predictionType,
      prediction: prediction.prediction || prediction.value || '',
      odds: odds,
      status: prediction.status || 'pending',
      createdAt: new Date(prediction.created_at || prediction.createdAt || new Date()),
      description: explanation,
      explanation: explanation,
      confidence: confidence,
      confidencePct: confidence,
      gameCode: prediction.game_code || prediction.gameCode || '',
      punterId: prediction.punter_id || prediction.punterId || 'ai-system',
      bookmaker: prediction.bookmaker || 'bet365',
      rolloverDay: prediction.rollover_day || prediction.rolloverDay || null,
      predictions: [],
      combined_odds: prediction.combined_odds || odds,
      combined_confidence: prediction.combined_confidence || confidence
    };
  });
}

/**
 * Get rollover predictions from the unified API endpoint
 * @param days Number of days for the rollover challenge
 * @returns A record of rollover predictions by day
 */
export async function getRolloverPredictions(days: number = 10): Promise<Record<number, Prediction[]>> {
  try {
    console.log(`Fetching rollover predictions for ${days} days`);
    
    // Fetch all categories from the unified endpoint
    const allCategories = await getAllCategoryPredictions();
    
    // Extract rollover predictions
    const rolloverPredictions = allCategories['rollover'] || [];
    
    // Group predictions by rollover day
    const result: Record<number, Prediction[]> = {};
    
    for (const prediction of rolloverPredictions) {
      const day = prediction.rolloverDay || 1;
      
      if (!result[day]) {
        result[day] = [];
      }
      
      result[day].push(prediction);
    }
    
    return result;
  } catch (error) {
    console.error(`Error fetching rollover predictions:`, error);
    throw error;
  }
}

export default {
  getAllCategoryPredictions,
  getRolloverPredictions
};
