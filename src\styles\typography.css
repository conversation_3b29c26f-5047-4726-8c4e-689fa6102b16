/* Typography Utility Classes */

/* Font Families */
.font-outfit {
  font-family: 'Outfit', sans-serif;
}

.font-jakarta {
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.font-jetbrains {
  font-family: 'JetBrains Mono', monospace;
}

.font-clash {
  font-family: 'Clash Display', sans-serif;
}

/* Heading Styles */
.heading-1 {
  font-family: 'Clash Display', sans-serif;
  font-weight: 600;
  font-size: 2.5rem;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.heading-2 {
  font-family: 'Clash Display', sans-serif;
  font-weight: 600;
  font-size: 2rem;
  line-height: 1.2;
  letter-spacing: -0.01em;
}

.heading-3 {
  font-family: 'Clash Display', sans-serif;
  font-weight: 500;
  font-size: 1.5rem;
  line-height: 1.3;
}

.heading-4 {
  font-family: 'Outfit', sans-serif;
  font-weight: 600;
  font-size: 1.25rem;
  line-height: 1.4;
}

/* Body Text Styles */
.body-large {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 400;
  font-size: 1.125rem;
  line-height: 1.6;
}

.body-medium {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.6;
}

.body-small {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.5;
}

.body-xs {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.5;
}

/* Code Text */
.code {
  font-family: 'JetBrains Mono', monospace;
  font-weight: 400;
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(to right, #F59E0B, #D97706);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Responsive Typography */
@media (max-width: 768px) {
  .heading-1 {
    font-size: 2rem;
  }
  
  .heading-2 {
    font-size: 1.75rem;
  }
  
  .heading-3 {
    font-size: 1.25rem;
  }
  
  .heading-4 {
    font-size: 1.125rem;
  }
  
  .body-large {
    font-size: 1rem;
  }
}
