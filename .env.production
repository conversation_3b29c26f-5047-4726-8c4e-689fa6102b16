# Production Environment Configuration for BetSightly Frontend

# Backend API Configuration (Production)
VITE_API_BASE_URL=https://your-render-app-name.onrender.com/api
VITE_API_TIMEOUT=30000

# Optional API Key (not enforced by backend but can be sent)
# VITE_API_KEY=your_production_api_key

# Cache Configuration (1 hour for production)
VITE_CACHE_DURATION=3600000

# Football API (if still needed)
VITE_FOOTBALL_API_KEY=your_football_api_key_here

# Production optimizations
NODE_ENV=production
